{{- if not .Values.service.disabled }}
{{- $sn := include "registry-frontend.fullname" . -}}
{{- $serviceName := .Values.service.name | default $sn }} 
# By default the service name matches fullnameOverride, but can be set to a different value
apiVersion: v1
kind: Service
metadata:
  name: {{ $serviceName }}
  {{- with .Values.service.annotations }} # Service annotations
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
{{ include "registry-frontend.labels" . | indent 4 }} # the typical app labels
spec:
  type: {{ .Values.service.type }} # By default ClusterIP, but it can be overwritten
  ports:
  {{- if .Values.service.ports }} #allow defining multiple ports
  {{- with .Values.service.ports }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- else }} 
# if ports block is not present it defines the default http port
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  {{- end }}
  selector:
    app.kubernetes.io/name: {{ $serviceName }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app: {{ .Values.app | default "o2"  }}
    name: {{ $serviceName }}
{{- if .Values.extraServices }} 
# defines extraServices, copies the text as is from the values files.
{{- range .Values.extraServices }} 
# iterates through all the defined entries of the current externalsecret
---
{{ toYaml . }}
{{- end }}


{{- end }}
{{- end }}
