{{- if and .Values.alb_ingress .Values.alb_ingress.enabled }}
{{- $fullName := include "registry-frontend.fullname" . -}}
{{- $targetPort := .Values.service.targetPort | default "http" }} # if no targetPort is defined, it defaults to http
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $fullName }}
  labels:
{{ include "registry-frontend.labels" . | indent 4 }}
  {{- with .Values.alb_ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
{{- if .Values.alb_ingress.className }}
  ingressClassName: {{ .Values.alb_ingress.className }}
{{- end }}
# {{- if .Values.alb_ingress.tls }} # if defined it can handle ssl connection
#   tls:
#   {{- range .Values.alb_ingress.tls }}
#     - hosts:
#       {{- range .hosts }}
#         - {{ . | quote }}
#       {{- end }}
#       secretName: {{ .secretName }}
#   {{- end }}
# {{- end }}
  rules:
  {{- range .Values.alb_ingress.hosts }} # multiple hosts can be defined
    - host: {{ .host | quote }}
      http:
        paths:
        {{- range .paths }} # multiple paths can be added, if needed
          - path: {{ .path }}
            pathType: {{ .pathType }}
            backend:
              service:
                name: {{ .service }}
                port:
                  name: {{ .port }}
        {{- end }}
  {{- end }}
{{- end }}
