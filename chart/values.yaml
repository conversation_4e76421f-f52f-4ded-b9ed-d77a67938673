# Application Configuration
nameOverride: scanconfig-service
fullnameOverride: scanconfig-service
replicaCount: 1

# Image Configuration
image:
  registry: "************.dkr.ecr.us-east-1.amazonaws.com"
  name: "veracode/sca-scanconfig-service"
  tag: "ATL-3809-acc54e9d"

imageConfig:
  pullPolicy: IfNotPresent

imagePullSecrets: []
initContainerConfig: {}
automountServiceAccountToken: true

# Deployment Strategy
progressDeadlineSeconds: 180
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0

# Container Configuration
command: "[java]"
args: [-jar, /data/sca-scanconfig-service.jar]

ports: |
  - name: https
    containerPort: 8506
    protocol: TCP
  - name: actuator
    containerPort: 8606
    protocol: TCP

# Resource Configuration
resources:
  requests:
    cpu: 256m
    memory: 512Mi

# Health Checks
readinessProbe: |
  httpGet:
    path: /sca/scanconfig/actuator/health
    port: actuator
  initialDelaySeconds: 90

livenessProbe: |
  httpGet:
    path: /sca/scanconfig/actuator/health
    port: actuator
  initialDelaySeconds: 90

# Pod Configuration
podLabels:
  admission.datadoghq.com/enabled: "true"

nodeSelector: {}
affinity: {}

# Persistence Configuration
persistence:
  enabled: false
  accessMode: ReadWriteOnce
  size: 1Gi



secret:
  - name: sca-scanconfig-service-secrets
    type: Opaque
    data:
      SPRING_DATASOURCE_USERNAME: c2NhbmNvbmZpZw==
      SPRING_DATASOURCE_PASSWORD: c2NhbmNvbmZpZw==
      SERVER_SSL_KEY_STORE_PASSWORD: Y2hhbmdlaXQ=
      SERVER_SSL_TRUST_STORE_PASSWORD: Y2hhbmdlaXQ= 
  - name: sca-scanconfig-service-certs
    type: Opaque
    data:
      sca-scanconfig-service-keystore.jks: 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
      truststore.jks: 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
      

service:
  type: ClusterIP
  targetPort: https
  ports:
    - port: 443
      name: https
      protocol: TCP
      targetPort: https

progressDeadlineSeconds: 180
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 0

command: "[java]"
args: [-jar, /data/sca-scanconfig-service.jar]

ports: |
  - name: https
    containerPort: 8506
    protocol: TCP
  - name: actuator
    containerPort: 8606
    protocol: TCP

podLabels:
  # allow datadog to inject variables required for APM
  admission.datadoghq.com/enabled: "true"

env:
  LOGGING_LEVEL_DATADOG_TRACE_AGENT_COMMON_WRITER_DDAGENT_DDAGENTAPI: FATAL
  LOGGING_LEVEL_COM_DATADOG_PROFILING_UPLOADER_PROFILEUPLOADER: FATAL
  AGORA_SERVER_SSL_CLIENTS_ALLOWED: '*'
  APPLICATION_JMS_SCAN_MESSAGE_ENABLED: "true"
  APPLICATION_JMS_SCAN_MESSAGE_QUEUE: __ENV_BRANCH__-upload-scan-config
  APPLICATION_JMS_SCAN_REQUEST_ENABLED: "true"
  APPLICATION_SCAN_RESULT_URL: https://sca-scanresult-service
  APPLICATION_SCAN_RESULT_NOTIFY_ENABLED: "true"
  APPLICATION_SCHEDULED_JOB_ENABLE: "true"
  AWS_KMS_ENABLED: 'true'
  AWS_SECRETSMANAGER_ENABLED: 'false'
  ENVIRONMENT: qa
  SERVER_SSL_KEY_STORE: 'file:config/sca-scanconfig-service-keystore.jks'
  SERVER_SSL_TRUST_STORE: 'file:config/truststore.jks'
  SERVER_SSL_CLIENT_AUTH: want
  SPRING_DATASOURCE_URL: ********************************************************************************************************************************************
  VOSP_MESSAGING_AWS_ACCOUNT: "************"
  VOSP_MESSAGING_ENVIRONMENT: agora-__ENV_BRANCH__
  # DataDog variables --------------------------------------------------------------------------
  DD_TAGS: "env:__ENV_NAME__ stack_name:__ENV_BRANCH__"
  DD_SERVICE: "sca-scanconfig-service"
  DD_PROFILING_ENABLED: __DD_PROFILING_ENABLED__
  JAVA_TOOL_OPTIONS: "-javaagent:/datadog/dd-java-agent.jar"

valueFrom:
  - name: POD_NAMESPACE
    valueFrom:
      fieldRef:
        fieldPath: metadata.namespace
envFrom: |
  - secretRef:
      name: sca-scanconfig-service-secrets
volumeMounts:
  - name: sca-scanconfig-cert-mount
    mountPath: "/data/config/sca-scanconfig-service-keystore.jks"
    subPath: sca-scanconfig-service-keystore.jks
    readOnly: true
  - name: sca-scanconfig-cert-mount
    mountPath: "/data/config/truststore.jks"
    subPath: truststore.jks
    readOnly: true
resources:
  requests:
    cpu: 256m
    memory: 512Mi
readinessProbe: |
  httpGet:
    path: /sca/scanconfig/actuator/health
    port: actuator
  initialDelaySeconds: 90
livenessProbe: |
  httpGet:
    path: /sca/scanconfig/actuator/health
    port: actuator
  initialDelaySeconds: 90
volumes:
  - name: sca-scanconfig-cert-mount
    secret:
      defaultMode: 420
      secretName: sca-scanconfig-service-certs 

serviceAccount:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/__ENV_BRANCH__-upload-user
  name: sca-scanconfig-service

ingress:
  enabled: false
