{{- /*
creates pvc unless disabled or an existingClaim was specified
*/ -}}
{{- if and .Values.persistence.enabled (not .Values.persistence.existingClaim) }} 



{{- $fullName := include "registry-frontend.fullname" . -}}
kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: {{ $fullName }}
  labels:
{{ include "registry-frontend.labels" . | indent 4 }}
spec:
  accessModes:
    - {{ .Values.persistence.accessMode | quote }}
  resources:
    requests:
      storage: {{ .Values.persistence.size | quote }} 
      # configure volume size
{{- if .Values.persistence.storageClass }}
{{- if (eq "-" .Values.persistence.storageClass) }} 
# Can override the default storageClass
  storageClassName: ""
{{- else }}
  storageClassName: "{{ .Values.persistence.storageClass }}"
{{- end }}
{{- end }}
{{- end }}

