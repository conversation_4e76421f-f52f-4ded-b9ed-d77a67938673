apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "registry-frontend.fullname" . }}-test-connection"
  labels:
{{ include "registry-frontend.labels" . | indent 4 }}
  annotations:
    "helm.sh/hook": test-success
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args:  ['{{ include "registry-frontend.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never

